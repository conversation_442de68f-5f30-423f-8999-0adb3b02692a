oa-prod$ npm test -- --testPathPattern="AtomicCircularBufferEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBufferEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts (395 MB heap size)
  AtomicCircularBufferEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (6 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle buffer overflow like base class (3 ms)
    Advanced Buffer Strategies
      ✓ should evict items using LRU policy (2 ms)
      ✓ should evict items using LFU policy (2 ms)
      ✓ should maintain performance with enhanced eviction (17 ms)
      ✓ should call pre-eviction callback when configured (3 ms)
    Buffer Persistence
      ✓ should create and restore from snapshot (3 ms)
      ✓ should validate snapshot integrity (27 ms)
      ✓ should handle snapshot creation performance requirements (8 ms)
    Buffer Analytics
      ✓ should provide comprehensive analytics (4 ms)
      ✓ should calculate hit and miss rates correctly (2 ms)
      ✓ should identify hot and cold items correctly (3 ms)
      ✓ should analyze access patterns (7 ms)
      ✓ should calculate analytics within performance requirements (2 ms)
    Buffer Optimization
      ✓ should generate and apply optimization recommendations (1 ms)
      ✓ should optimize eviction policy when needed (1 ms)
    Performance Validation
      ✓ should meet enhanced operation performance requirements (2 ms)
      ✓ should maintain memory overhead within reasonable limits (2 ms)
      ✓ should handle concurrent access without performance degradation (19 ms)
    Error Handling and Edge Cases
      ✕ should handle zero-size buffer gracefully (3 ms)
      ✓ should handle invalid snapshot gracefully (2 ms)
      ✓ should handle custom eviction function errors (2 ms)

  ● AtomicCircularBufferEnhanced › Error Handling and Edge Cases › should handle zero-size buffer gracefully

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      551 |       try {
      552 |         await zeroBuffer.addItem('key', 'value');
    > 553 |         expect(zeroBuffer.getSize()).toBe(0);
          |                                      ^
      554 |         expect(zeroBuffer.getItem('key')).toBeUndefined();
      555 |
      556 |         const analytics = zeroBuffer.getBufferAnalytics();

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:553:38)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 22 passed, 23 total
Snapshots:   0 total
Time:        3.647 s
Ran all test suites matching /AtomicCircularBufferEnhanced.test.ts/i.