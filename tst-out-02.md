oa-prod$ npm test -- --testPathPattern="EventHandlerRegistryEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistryEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts (122.056 s, 221 MB heap size)
  EventHandlerRegistryEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (11 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle handler unregistration like base class (2 ms)
    Event Emission System
      ✓ should emit events to all registered handlers (4 ms)
      ✓ should handle handler errors gracefully (2 ms)
      ✓ should emit events to specific clients (2 ms)
      ✓ should process event batches correctly (3 ms)
      ✕ should handle event emission timeout (30005 ms)
      ✓ should meet performance requirements for emission (7 ms)
    Handler Middleware System
      ✓ should execute middleware in priority order (6 ms)
      ✓ should skip handler when middleware returns false (5 ms)
      ✓ should handle errors through middleware (4 ms)
      ✓ should execute after-handler middleware (3 ms)
      ✓ should remove middleware correctly (4 ms)
      ✕ should meet middleware performance requirements (29 ms)
    Advanced Handler Deduplication
      ✓ should detect duplicate handlers by reference (3 ms)
      ✓ should detect duplicate handlers by signature (5 ms)
      ✓ should use custom deduplication function (3 ms)
      ✓ should merge metadata on duplicate detection (5 ms)
      ✓ should meet deduplication performance requirements (2 ms)
    Event Buffering and Queuing
      ✕ should buffer events and flush periodically (30009 ms)
      ✕ should auto-flush when threshold is reached (30002 ms)
      ✕ should handle buffer overflow correctly (4 ms)
      ✕ should process events with priority strategy (30006 ms)
      ✓ should meet buffer operation performance requirements (6 ms)
    Enhanced Metrics and Monitoring
      ✓ should provide enhanced metrics (4 ms)
      ✓ should track middleware execution metrics (3 ms)

  ● EventHandlerRegistryEnhanced › Event Emission System › should handle event emission timeout

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      211 |     });
      212 |
    > 213 |     it('should handle event emission timeout', async () => {
          |     ^
      214 |       registry.registerHandler('client1', 'test-event', async () => {
      215 |         // Simulate slow handler
      216 |         await new Promise(resolve => setTimeout(resolve, 100));

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:213:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:125:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

  ● EventHandlerRegistryEnhanced › Handler Middleware System › should meet middleware performance requirements

    expect(received).toBeLessThan(expected)

    Expected: < 5
    Received:   25.890589999999065

      377 |
      378 |       // Performance requirement: <2ms per middleware
    > 379 |       expect(duration).toBeLessThan(5); // Allow some buffer for test environment
          |                        ^
      380 |     });
      381 |   });
      382 |

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:379:24)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should buffer events and flush periodically

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      510 |     });
      511 |
    > 512 |     it('should buffer events and flush periodically', async () => {
          |     ^
      513 |       let handlerCallCount = 0;
      514 |
      515 |       registry.registerHandler('client1', 'test-event', () => {

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:512:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:496:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should auto-flush when threshold is reached

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      541 |     });
      542 |
    > 543 |     it('should auto-flush when threshold is reached', async () => {
          |     ^
      544 |       let handlerCallCount = 0;
      545 |
      546 |       registry.registerHandler('client1', 'test-event', () => {

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:543:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:496:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should handle buffer overflow correctly

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 4

      592 |
      593 |       // Handler shouldn't be called yet (events are buffered)
    > 594 |       expect(handlerCallCount).toBe(0);
          |                                ^
      595 |     });
      596 |
      597 |     it('should process events with priority strategy', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:594:32)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should process events with priority strategy

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      595 |     });
      596 |
    > 597 |     it('should process events with priority strategy', async () => {
          |     ^
      598 |       const processedEvents: any[] = [];
      599 |
      600 |       registry.registerHandler('client1', 'test-event', (data: unknown) => {

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:597:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:496:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

Test Suites: 1 failed, 1 total
Tests:       6 failed, 21 passed, 27 total
Snapshots:   0 total
Time:        122.328 s, estimated 124 s
Ran all test suites matching /EventHandlerRegistryEnhanced.test.ts/i.