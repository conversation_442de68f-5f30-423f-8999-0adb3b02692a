npm test -- --testPathPattern="AtomicCircularBufferEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBufferEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts (229 MB heap size)
  AtomicCircularBufferEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (6 ms)
      ✓ should preserve base class metrics functionality (9 ms)
      ✓ should handle buffer overflow like base class (3 ms)
    Advanced Buffer Strategies
      ✕ should evict items using LRU policy (4 ms)
      ✓ should evict items using LFU policy (3 ms)
      ✓ should maintain performance with enhanced eviction (14 ms)
      ✓ should call pre-eviction callback when configured (3 ms)
    Buffer Persistence
      ✓ should create and restore from snapshot (3 ms)
      ✓ should validate snapshot integrity (21 ms)
      ✓ should handle snapshot creation performance requirements (3 ms)
    Buffer Analytics
      ✓ should provide comprehensive analytics (4 ms)
      ✓ should calculate hit and miss rates correctly (2 ms)
      ✓ should identify hot and cold items correctly (2 ms)
      ✓ should analyze access patterns (23 ms)
      ✓ should calculate analytics within performance requirements (2 ms)
    Buffer Optimization
      ✓ should generate and apply optimization recommendations (2 ms)
      ✓ should optimize eviction policy when needed (2 ms)
    Performance Validation
      ✓ should meet enhanced operation performance requirements (2 ms)
      ✓ should maintain memory overhead within reasonable limits (3 ms)
      ✓ should handle concurrent access without performance degradation (16 ms)
    Error Handling and Edge Cases
      ✓ should handle zero-size buffer gracefully (6 ms)
      ✓ should handle invalid snapshot gracefully (2 ms)
      ✓ should handle custom eviction function errors (1 ms)

  ● AtomicCircularBufferEnhanced › Advanced Buffer Strategies › should evict items using LRU policy

    expect(received).toBe(expected) // Object.is equality

    Expected: "value1"
    Received: undefined

      120 |         console.log('Buffer size:', lruBuffer.getSize());
      121 |
    > 122 |         expect(lruBuffer.getItem('key1')).toBe('value1');
          |                                           ^
      123 |         expect(lruBuffer.getItem('key2')).toBeUndefined();
      124 |         expect(lruBuffer.getItem('key3')).toBe('value3');
      125 |         expect(lruBuffer.getItem('key4')).toBe('value4');

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:122:43)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 22 passed, 23 total
Snapshots:   0 total
Time:        2.25 s, estimated 4 s
Ran all test suites matching /AtomicCircularBufferEnhanced.test.ts/i.
