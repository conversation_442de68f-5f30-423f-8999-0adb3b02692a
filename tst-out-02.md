oa-prod$ npm test -- --testPathPattern="EventHandlerRegistryEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=EventHandlerRegistryEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts (33.302 s, 379 MB heap size)
  EventHandlerRegistryEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (6 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle handler unregistration like base class (3 ms)
    Event Emission System
      ✓ should emit events to all registered handlers (8 ms)
      ✓ should handle handler errors gracefully (2 ms)
      ✓ should emit events to specific clients (2 ms)
      ✓ should process event batches correctly (3 ms)
      ✕ should handle event emission timeout (30004 ms)
      ✓ should meet performance requirements for emission (7 ms)
    Handler Middleware System
      ✓ should execute middleware in priority order (5 ms)
      ✓ should skip handler when middleware returns false (6 ms)
      ✓ should handle errors through middleware (4 ms)
      ✓ should execute after-handler middleware (3 ms)
      ✓ should remove middleware correctly (5 ms)
      ✓ should meet middleware performance requirements (4 ms)
    Advanced Handler Deduplication
      ✓ should detect duplicate handlers by reference (3 ms)
      ✓ should detect duplicate handlers by signature (4 ms)
      ✓ should use custom deduplication function (6 ms)
      ✓ should merge metadata on duplicate detection (4 ms)
      ✓ should meet deduplication performance requirements (5 ms)
    Event Buffering and Queuing
      ✓ should buffer events and flush periodically (17 ms)
      ✓ should auto-flush when threshold is reached (3 ms)
      ✓ should handle buffer overflow correctly (5 ms)
      ✓ should process events with priority strategy (5 ms)
      ✓ should meet buffer operation performance requirements (11 ms)
    Enhanced Metrics and Monitoring
      ✓ should provide enhanced metrics (4 ms)
      ✓ should track middleware execution metrics (2 ms)

  ● EventHandlerRegistryEnhanced › Event Emission System › should handle event emission timeout

    thrown: "Exceeded timeout of 30000 ms for a test while waiting for `done()` to be called.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      211 |     });
      212 |
    > 213 |     it('should handle event emission timeout', (done) => {
          |     ^
      214 |       // ANTI-SIMPLIFICATION COMPLIANCE: Complete timeout functionality with Jest callback-based testing
      215 |
      216 |       let handlerStarted = false;

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:213:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:125:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 26 passed, 27 total
Snapshots:   0 total
Time:        33.565 s
Ran all test suites matching /EventHandlerRegistryEnhanced.test.ts/i.
