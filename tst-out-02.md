oa-prod$ npm test -- --testPathPattern="AtomicCircularBufferEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBufferEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts (402 MB heap size)
  AtomicCircularBufferEnhanced
    Backward Compatibility
      ✕ should maintain all base class functionality (5 ms)
      ✕ should preserve base class metrics functionality (3 ms)
      ✕ should handle buffer overflow like base class (3 ms)
    Advanced Buffer Strategies
      ✕ should evict items using LRU policy (2 ms)
      ✕ should evict items using LFU policy (2 ms)
      ✕ should maintain performance with enhanced eviction (2 ms)
      ✕ should call pre-eviction callback when configured (2 ms)
    Buffer Persistence
      ✕ should create and restore from snapshot (2 ms)
      ✕ should validate snapshot integrity (2 ms)
      ✕ should handle snapshot creation performance requirements (2 ms)
    Buffer Analytics
      ✕ should provide comprehensive analytics (2 ms)
      ✕ should calculate hit and miss rates correctly (2 ms)
      ✕ should identify hot and cold items correctly (2 ms)
      ✕ should analyze access patterns (2 ms)
      ✕ should calculate analytics within performance requirements (2 ms)
    Buffer Optimization
      ✕ should generate and apply optimization recommendations (1 ms)
      ✕ should optimize eviction policy when needed (1 ms)
    Performance Validation
      ✕ should meet enhanced operation performance requirements (1 ms)
      ✕ should maintain memory overhead within reasonable limits (1 ms)
      ✕ should handle concurrent access without performance degradation (1 ms)
    Error Handling and Edge Cases
      ✓ should handle zero-size buffer gracefully (3 ms)
      ✓ should handle invalid snapshot gracefully (2 ms)
      ✕ should handle custom eviction function errors (1 ms)

  ● AtomicCircularBufferEnhanced › Backward Compatibility › should maintain all base class functionality

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:54:20)

  ● AtomicCircularBufferEnhanced › Backward Compatibility › should preserve base class metrics functionality

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:68:20)

  ● AtomicCircularBufferEnhanced › Backward Compatibility › should handle buffer overflow like base class

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:80:22)

  ● AtomicCircularBufferEnhanced › Advanced Buffer Strategies › should evict items using LRU policy

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:107:25)

  ● AtomicCircularBufferEnhanced › Advanced Buffer Strategies › should evict items using LFU policy

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:140:25)

  ● AtomicCircularBufferEnhanced › Advanced Buffer Strategies › should maintain performance with enhanced eviction

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:172:28)

  ● AtomicCircularBufferEnhanced › Advanced Buffer Strategies › should call pre-eviction callback when configured

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:197:30)

  ● AtomicCircularBufferEnhanced › Buffer Persistence › should create and restore from snapshot

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:217:20)

  ● AtomicCircularBufferEnhanced › Buffer Persistence › should validate snapshot integrity

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:240:20)

  ● AtomicCircularBufferEnhanced › Buffer Persistence › should handle snapshot creation performance requirements

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:259:22)

  ● AtomicCircularBufferEnhanced › Buffer Analytics › should provide comprehensive analytics

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:279:20)

  ● AtomicCircularBufferEnhanced › Buffer Analytics › should calculate hit and miss rates correctly

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:306:20)

  ● AtomicCircularBufferEnhanced › Buffer Analytics › should identify hot and cold items correctly

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:325:20)

  ● AtomicCircularBufferEnhanced › Buffer Analytics › should analyze access patterns

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:358:20)

  ● AtomicCircularBufferEnhanced › Buffer Analytics › should calculate analytics within performance requirements

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:377:22)

  ● AtomicCircularBufferEnhanced › Buffer Optimization › should generate and apply optimization recommendations

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:399:20)

  ● AtomicCircularBufferEnhanced › Buffer Optimization › should optimize eviction policy when needed

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:424:32)

  ● AtomicCircularBufferEnhanced › Performance Validation › should meet enhanced operation performance requirements

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:455:26)

  ● AtomicCircularBufferEnhanced › Performance Validation › should maintain memory overhead within reasonable limits

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:483:28)

  ● AtomicCircularBufferEnhanced › Performance Validation › should handle concurrent access without performance degradation

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:515:34)

  ● AtomicCircularBufferEnhanced › Error Handling and Edge Cases › should handle custom eviction function errors

    Immediate sync validation failed: map=0, array=1

      454 |       this._metrics.syncErrors++;
      455 |       this._metrics.lastSyncError = new Date();
    > 456 |       throw new Error(`Immediate sync validation failed: map=${mapSize}, array=${arrayLength}`);
          |             ^
      457 |     }
      458 |   }
      459 |

      at AtomicCircularBufferEnhanced._validateSyncImmediate (shared/src/base/AtomicCircularBuffer.ts:456:13)
      at shared/src/base/AtomicCircularBufferEnhanced.ts:311:21
      at AtomicCircularBufferEnhanced._withLock (shared/src/base/AtomicCircularBuffer.ts:372:22)
      at AtomicCircularBufferEnhanced.addItem (shared/src/base/AtomicCircularBufferEnhanced.ts:274:25)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts:588:27)

Test Suites: 1 failed, 1 total
Tests:       21 failed, 2 passed, 23 total
Snapshots:   0 total
Time:        3.729 s
Ran all test suites matching /AtomicCircularBufferEnhanced.test.ts/i.
